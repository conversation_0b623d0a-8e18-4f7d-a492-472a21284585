import { useState } from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Checkbox,
  IconButton,
  Chip,
  Tooltip,
  TablePagination,
  Box,
  Typography
} from '@mui/material';
import {
  Edit as EditIcon,
  Delete as DeleteIcon,
  Visibility as ViewIcon,
  Psychology as PsychologyIcon,
  Assessment as AssessmentIcon
} from '@mui/icons-material';
import type { Persona } from '@/types';

interface PersonasTableProps {
  personas: Persona[];
  loading?: boolean;
  onEdit: (persona: Persona) => void;
  onDelete: (persona: Persona) => void;
  onView: (persona: Persona) => void;
  onBulkDelete: (ids: number[]) => void;
  onCuestionario?: (persona: Persona) => void;
  onVerReporte?: (persona: Persona) => void;
  currentUserRole?: string;
}

const PersonasTable = ({
  personas,
  loading = false,
  onEdit,
  onDelete,
  onView,
  onBulkDelete,
  onCuestionario,
  onVerReporte,
  currentUserRole
}: PersonasTableProps) => {
  const [selected, setSelected] = useState<number[]>([]);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);

  const handleSelectAll = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.checked) {
      const newSelected = personas.map((persona) => persona.id);
      setSelected(newSelected);
    } else {
      setSelected([]);
    }
  };

  const handleSelect = (id: number) => {
    const selectedIndex = selected.indexOf(id);
    let newSelected: number[] = [];

    if (selectedIndex === -1) {
      newSelected = newSelected.concat(selected, id);
    } else if (selectedIndex === 0) {
      newSelected = newSelected.concat(selected.slice(1));
    } else if (selectedIndex === selected.length - 1) {
      newSelected = newSelected.concat(selected.slice(0, -1));
    } else if (selectedIndex > 0) {
      newSelected = newSelected.concat(
        selected.slice(0, selectedIndex),
        selected.slice(selectedIndex + 1)
      );
    }

    setSelected(newSelected);
  };

  const handleChangePage = (event: unknown, newPage: number) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const getRolColor = (rol: string) => {
    switch (rol) {
      case 'admin':
        return 'error';
      case 'personal':
        return 'warning';
      case 'docente':
        return 'info';
      case 'alumno':
        return 'success';
      default:
        return 'default';
    }
  };

  const isSelected = (id: number) => selected.indexOf(id) !== -1;

  const emptyRows = page > 0 ? Math.max(0, (1 + page) * rowsPerPage - personas.length) : 0;

  return (
    <Paper sx={{ width: '100%', overflow: 'hidden' }}>
      {selected.length > 0 && (
        <Box sx={{ p: 2, bgcolor: 'primary.light', color: 'primary.contrastText' }}>
          <Typography variant="subtitle1">
            {selected.length} elemento(s) seleccionado(s)
          </Typography>
        </Box>
      )}
      
      <TableContainer sx={{ maxHeight: 440 }}>
        <Table stickyHeader aria-label="tabla de personas">
          <TableHead>
            <TableRow>
              <TableCell padding="checkbox">
                <Checkbox
                  color="primary"
                  indeterminate={selected.length > 0 && selected.length < personas.length}
                  checked={personas.length > 0 && selected.length === personas.length}
                  onChange={handleSelectAll}
                />
              </TableCell>
              <TableCell>ID</TableCell>
              <TableCell>Correo Institucional</TableCell>
              <TableCell>Matrícula</TableCell>
              <TableCell>Tipo</TableCell>
              <TableCell>Rol</TableCell>
              <TableCell>Estado</TableCell>
              <TableCell>Acciones</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {personas
              .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
              .map((persona) => {
                const isItemSelected = isSelected(persona.id);
                return (
                  <TableRow
                    hover
                    onClick={() => handleSelect(persona.id)}
                    role="checkbox"
                    aria-checked={isItemSelected}
                    tabIndex={-1}
                    key={persona.id}
                    selected={isItemSelected}
                  >
                    <TableCell padding="checkbox">
                      <Checkbox
                        color="primary"
                        checked={isItemSelected}
                      />
                    </TableCell>
                    <TableCell>{persona.id}</TableCell>
                    <TableCell>{persona.correo_institucional}</TableCell>
                    <TableCell>{persona.matricula || 'N/A'}</TableCell>
                    <TableCell>{persona.tipo_persona}</TableCell>
                    <TableCell>
                      <Chip
                        label={persona.rol}
                        color={getRolColor(persona.rol) as any}
                        size="small"
                      />
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={persona.is_active ? 'Activo' : 'Inactivo'}
                        color={persona.is_active ? 'success' : 'error'}
                        size="small"
                      />
                    </TableCell>
                    <TableCell>
                      <Tooltip title="Ver">
                        <IconButton
                          size="small"
                          onClick={(e) => {
                            e.stopPropagation();
                            onView(persona);
                          }}
                        >
                          <ViewIcon />
                        </IconButton>
                      </Tooltip>
                      <Tooltip title="Editar">
                        <IconButton
                          size="small"
                          onClick={(e) => {
                            e.stopPropagation();
                            onEdit(persona);
                          }}
                        >
                          <EditIcon />
                        </IconButton>
                      </Tooltip>
                      <Tooltip title="Eliminar">
                        <IconButton
                          size="small"
                          onClick={(e) => {
                            e.stopPropagation();
                            onDelete(persona);
                          }}
                        >
                          <DeleteIcon />
                        </IconButton>
                      </Tooltip>

                      {/* Botón de cuestionario para estudiantes */}
                      {persona.tipo_persona === 'estudiante' && onCuestionario && (
                        <Tooltip title="Cuestionario Psicopedagógico">
                          <IconButton
                            size="small"
                            onClick={(e) => {
                              e.stopPropagation();
                              onCuestionario(persona);
                            }}
                            color="primary"
                          >
                            <PsychologyIcon />
                          </IconButton>
                        </Tooltip>
                      )}

                      {/* Botón de reporte para admin/personal */}
                      {persona.tipo_persona === 'estudiante' &&
                       onVerReporte &&
                       (currentUserRole === 'admin' || currentUserRole === 'personal') && (
                        <Tooltip title="Ver Reporte Psicopedagógico">
                          <IconButton
                            size="small"
                            onClick={(e) => {
                              e.stopPropagation();
                              onVerReporte(persona);
                            }}
                            color="secondary"
                          >
                            <AssessmentIcon />
                          </IconButton>
                        </Tooltip>
                      )}
                    </TableCell>
                  </TableRow>
                );
              })}
            {emptyRows > 0 && (
              <TableRow style={{ height: 53 * emptyRows }}>
                <TableCell colSpan={8} />
              </TableRow>
            )}
          </TableBody>
        </Table>
      </TableContainer>
      <TablePagination
        rowsPerPageOptions={[5, 10, 25]}
        component="div"
        count={personas.length}
        rowsPerPage={rowsPerPage}
        page={page}
        onPageChange={handleChangePage}
        onRowsPerPageChange={handleChangeRowsPerPage}
        labelRowsPerPage="Filas por página:"
        labelDisplayedRows={({ from, to, count }) =>
          `${from}-${to} de ${count !== -1 ? count : `más de ${to}`}`
        }
      />
    </Paper>
  );
};

export default PersonasTable;
