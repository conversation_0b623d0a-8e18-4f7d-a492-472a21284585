from sqlalchemy import Table, <PERSON><PERSON><PERSON>, <PERSON>teger, Foreign<PERSON>ey
from app.db.database import Base

# Tabla de asociación para la relación many-to-many entre Persona y Grupo
persona_grupo = Table(
    'persona_grupo',
    Base.metadata,
    <PERSON>umn('persona_id', <PERSON>teger, <PERSON><PERSON><PERSON>('personas.id'), primary_key=True),
    <PERSON>umn('grupo_id', Integer, Foreign<PERSON>ey('grupo.id'), primary_key=True)
)

# Tabla de asociación para la relación many-to-many entre Persona y ProgramaEducativo
persona_programa = Table(
    'persona_programa',
    Base.metadata,
    Column('persona_id', Integer, <PERSON><PERSON><PERSON>('personas.id'), primary_key=True),
    <PERSON>umn('programa_id', Integer, Foreign<PERSON>ey('programa_educativo.id'), primary_key=True)
)
