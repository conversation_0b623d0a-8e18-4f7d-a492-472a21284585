{"root": ["../../src/app.tsx", "../../src/config.ts", "../../src/main.tsx", "../../src/vite-env.d.ts", "../../src/components/alumnodashboard.tsx", "../../src/components/alumnoperfilform.tsx", "../../src/components/alumnoregistroform.tsx", "../../src/components/appointmentrequestmodal.tsx", "../../src/components/atencionform.tsx", "../../src/components/catalogoselector.tsx", "../../src/components/confirmdialog.tsx", "../../src/components/cuestionariopsicopedagogico.tsx", "../../src/components/estudiantescuestionarios.tsx", "../../src/components/grupoform.tsx", "../../src/components/loginform.tsx", "../../src/components/miscitas.tsx", "../../src/components/notificacionescitas.tsx", "../../src/components/personaform.tsx", "../../src/components/personastable.tsx", "../../src/components/programaeducativoform.tsx", "../../src/components/protectedroute.tsx", "../../src/components/reportepsicopedagogico.tsx", "../../src/components/solicitudcitaform.tsx", "../../src/components/solicitudescitas.tsx", "../../src/components/admin/catalogosadmin.tsx", "../../src/components/admin/notificacionespendientes.tsx", "../../src/contexts/authcontext.tsx", "../../src/pages/alumnopage.tsx", "../../src/pages/atencionespage.tsx", "../../src/pages/catalogospage.tsx", "../../src/pages/cuestionarioscompletadospage.tsx", "../../src/pages/dashboard.tsx", "../../src/pages/grupospage.tsx", "../../src/pages/loginpage.tsx", "../../src/pages/personaspage.tsx", "../../src/pages/programaseducativospage.tsx", "../../src/pages/registroalumnopage.tsx", "../../src/services/api.ts", "../../src/services/notificationservice.ts", "../../src/types/index.ts"], "errors": true, "version": "5.8.3"}